// Thêm cache và preload ở đầu file
let projectsCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // 10 giây
let preloadPromise = null;

import { saveAs } from "file-saver";
import jsPDF from "jspdf";
import { useEffect, useState } from "react";
import * as XLSX from "xlsx";

import { getAllProjects, getProjectTasks } from "../../../api/projectManagement";
import { getProjectMembers } from "../../../api/userManagement";
import chartIcon from "../../../assets/chart-column-decreasing.svg";
import completeIcon from "../../../assets/complete.svg";
import considerIcon from "../../../assets/consider.svg";
import deploymentIcon from "../../../assets/deployment.svg";
import downloadIcon from "../../../assets/download.svg";
import eyeIcon from "../../../assets/eye.svg";
import fileIcon from "../../../assets/file-text.svg";
import filterIcon from "../../../assets/filter.svg";
import jobIcon from "../../../assets/job.svg";
import timeIcon from "../../../assets/time.svg";
import triangleAlertIcon from "../../../assets/triangle-alert.svg";
import userGroupIcon from "../../../assets/users.svg";
import waitingIcon from "../../../assets/waiting.svg";
import "../../../styles/ListStatistical.css";
import DetailJob from "../../components/JobDetail";
import { DeleteProjectForm } from "../../components/ProjectDetailPopup";
import StatisticalCard from "../../components/StatisticalCard";
import StatisticalUpdate from "../../components/StatisticalUpdate";

// Thêm hàm định dạng ngày
function formatDate(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  if (isNaN(d)) return dateStr;
  return d.toLocaleDateString('vi-VN'); // Kết quả: 22/07/2025
}

// Helper to get status icon
const getStatusIcon = (status) => {
  const statusLower = String(status || '').toLowerCase();
  switch (statusLower) {
    case "in_progress":
    case "in-progress":
    case "đang triển khai":
      return deploymentIcon;
    case "completed":
    case "hoàn thành":
      return completeIcon;
    case "pending":
    case "waiting":
    case "đang chờ":
      return waitingIcon;
    case "overdue":
    case "quá hạn":
      return triangleAlertIcon;
    case "review":
    case "consider":
    case "đang xem xét":
      return considerIcon;
    default:
      return waitingIcon;
  }
};

const List = () => {
  const [projects, setProjects] = useState([]); // Dữ liệu dự án từ API
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedProject, setSelectedProject] = useState(null); //chọn
  const [deleteProject, setDeleteProject] = useState(null); //xóa
  const [editProject, setEditProject] = useState(null); // Thêm state cho form sửa

  // Lấy thông tin user hiện tại để kiểm tra quyền
  const getCurrentUser = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      return userRaw.user || userRaw;
    } catch {
      return {};
    }
  };

  const currentUser = getCurrentUser();
  const userRole = currentUser.role?.toLowerCase();

  // Kiểm tra quyền xóa - admin, CEO và departmentHead có quyền xóa
  const canDelete = ['admin', 'ceo', 'departmenthead'].includes(userRole);

  // Preload function
  const preloadProjects = async () => {
    if (preloadPromise) return preloadPromise;
    preloadPromise = (async () => {
      try {
        const projectsRes = await getAllProjects();
        const projects = projectsRes.data || [];
        
                 // Fetch members and tasks for each project
         const projectsWithMembers = await Promise.all(
           projects.map(async (project, index) => {
             try {
               const [membersRes, tasksRes] = await Promise.all([
                 getProjectMembers(project.id || project._id),
                 getProjectTasks(project.id || project._id).catch((error) => {
                   console.warn(`Failed to fetch tasks for project ${project.id || project._id}:`, error);
                   return { data: [] };
                 })
               ]);
               
                                // Kiểm tra trạng thái tất cả công việc trong dự án
                 const projectTasks = tasksRes.data || [];
                 const totalTasks = projectTasks.length;
                 const completedTasks = projectTasks.filter(task => {
                   const status = String(task.status || '').toLowerCase();
                   const progress = task.progress || 0;
                   return status === 'completed' || 
                          status === 'hoàn thành' ||
                          progress >= 100;
                 }).length;
                 
                 // Debug: Log thông tin tasks cho dự án cụ thể
                 if (project.name === 'test q') {
                   console.log(`Project ${project.name} tasks:`, {
                     projectId: project.id || project._id,
                     totalTasks,
                     completedTasks,
                     tasks: projectTasks.map(t => ({ 
                       id: t.id, 
                       status: t.status, 
                       progress: t.progress,
                       title: t.title 
                     }))
                   });
                 }
               
               // Tính toán trạng thái dự án dựa trên công việc
               let calculatedStatus;
               let calculatedProgress = 0;
               
               if (totalTasks === 0) {
                 // Dự án không có công việc - sử dụng fallback từ project data
                 if (project.progress !== undefined && project.progress !== null) {
                   calculatedProgress = project.progress;
                   if (calculatedProgress >= 100) {
                     calculatedStatus = 'completed';
                   } else if (calculatedProgress > 0) {
                     calculatedStatus = 'in-progress';
                   } else {
                     calculatedStatus = 'waiting';
                   }
                 } else if (project.status) {
                   // Sử dụng status nếu không có progress
                   const status = String(project.status).toLowerCase();
                   if (status === 'completed' || status === 'hoàn thành') {
                     calculatedStatus = 'completed';
                     calculatedProgress = 100;
                   } else if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') {
                     calculatedStatus = 'in-progress';
                     calculatedProgress = 50;
                   } else if (status === 'review' || status === 'consider' || status === 'đang xem xét') {
                     calculatedStatus = 'review';
                     calculatedProgress = 75;
                   } else {
                     calculatedStatus = 'waiting';
                     calculatedProgress = 0;
                   }
                 } else {
                   calculatedStatus = 'waiting';
                   calculatedProgress = 0;
                 }
               } else if (completedTasks === totalTasks && totalTasks > 0) {
                 // Tất cả công việc đã hoàn thành
                 calculatedStatus = 'completed';
                 calculatedProgress = 100;
               } else if (completedTasks > 0 && completedTasks < totalTasks) {
                 // Một số công việc đã hoàn thành
                 calculatedStatus = 'in-progress';
                 calculatedProgress = Math.round((completedTasks / totalTasks) * 100);
               } else {
                 // Chưa có công việc nào hoàn thành
                 calculatedStatus = 'waiting';
                 calculatedProgress = 0;
               }
              
              return {
                ...project,
                status: calculatedStatus,
                progress: calculatedProgress,
                members: membersRes.data || [],
              };
            } catch {
              return {
                ...project,
                status: 'waiting',
                progress: 0,
                members: [],
              };
            }
          })
        );
        projectsCache = projectsWithMembers;
        cacheTimestamp = Date.now();
        return projectsWithMembers;
      } catch {
        return [];
      }
    })();
    return preloadPromise;
  };

  useEffect(() => {
    async function fetchProjects() {
      const now = Date.now();
      if (projectsCache && (now - cacheTimestamp) < CACHE_DURATION) {
        setProjects(projectsCache);
        setLoading(false);
        return;
      }
      setLoading(true);
      let allProjects = [];
      if (preloadPromise) {
        allProjects = await preloadPromise;
      } else {
        try {
          const projectsRes = await getAllProjects();
          const projects = projectsRes.data || [];
                     // Fetch members and tasks for each project
           allProjects = await Promise.all(
             projects.map(async (project, index) => {
               try {
                 const [membersRes, tasksRes] = await Promise.all([
                   getProjectMembers(project.id || project._id),
                   getProjectTasks(project.id || project._id).catch((error) => {
                     console.warn(`Failed to fetch tasks for project ${project.id || project._id}:`, error.message);
                     return { data: [] };
                   })
                 ]);
                 

                 
                 // Kiểm tra trạng thái tất cả công việc trong dự án
                 const projectTasks = tasksRes.data || [];
                 const totalTasks = projectTasks.length;
                 const completedTasks = projectTasks.filter(task => {
                   const status = String(task.status || '').toLowerCase();
                   const progress = task.progress || 0;
                   return status === 'completed' || 
                          status === 'hoàn thành' ||
                          progress >= 100;
                 }).length;
                 
                 // Tính toán trạng thái dự án dựa trên công việc
                 let calculatedStatus;
                 let calculatedProgress = 0;
                 
                 if (totalTasks === 0) {
                   // Dự án không có công việc - sử dụng fallback từ project data
                   if (project.progress !== undefined && project.progress !== null) {
                     calculatedProgress = project.progress;
                     if (calculatedProgress >= 100) {
                       calculatedStatus = 'completed';
                     } else if (calculatedProgress > 0) {
                       calculatedStatus = 'in-progress';
                     } else {
                       calculatedStatus = 'waiting';
                     }
                   } else if (project.status) {
                     // Sử dụng status nếu không có progress
                     const status = String(project.status).toLowerCase();
                     if (status === 'completed' || status === 'hoàn thành') {
                       calculatedStatus = 'completed';
                       calculatedProgress = 100;
                     } else if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') {
                       calculatedStatus = 'in-progress';
                       calculatedProgress = 50;
                     } else if (status === 'review' || status === 'consider' || status === 'đang xem xét') {
                       calculatedStatus = 'review';
                       calculatedProgress = 75;
                     } else {
                       calculatedStatus = 'waiting';
                       calculatedProgress = 0;
                     }
                   } else {
                     calculatedStatus = 'waiting';
                     calculatedProgress = 0;
                   }
                 } else if (completedTasks === totalTasks && totalTasks > 0) {
                   // Tất cả công việc đã hoàn thành
                   calculatedStatus = 'completed';
                   calculatedProgress = 100;
                 } else if (completedTasks > 0 && completedTasks < totalTasks) {
                   // Một số công việc đã hoàn thành
                   calculatedStatus = 'in-progress';
                   calculatedProgress = Math.round((completedTasks / totalTasks) * 100);
                 } else {
                   // Chưa có công việc nào hoàn thành
                   calculatedStatus = 'waiting';
                   calculatedProgress = 0;
                 }
                
                return {
                  ...project,
                  status: calculatedStatus,
                  progress: calculatedProgress,
                  members: membersRes.data || [],
                };
              } catch {
                const fallbackStatus = 'waiting';
                const fallbackProgress = fallbackStatus === 'completed' ? 100 : 
                                      fallbackStatus === 'in-progress' ? 50 : 
                                      fallbackStatus === 'review' ? 75 : 0;
                return {
                  ...project,
                  status: fallbackStatus,
                  progress: fallbackProgress,
                  members: [],
                };
              }
            })
          );
        } catch {
          allProjects = [];
        }
      }
      setProjects(allProjects);
      projectsCache = allProjects;
      cacheTimestamp = Date.now();
      setLoading(false);
    }
    fetchProjects();
  }, [window.location.pathname]);

  useEffect(() => { preloadProjects(); }, []);

  // Add event listeners to refresh data when projects/tasks are updated
  useEffect(() => {
    const handleProjectUpdate = () => {
      // Clear cache to force refresh
      projectsCache = null;
      cacheTimestamp = 0;
      preloadPromise = null;
      // Refetch projects
      async function refetchProjects() {
        const now = Date.now();
        setLoading(true);
        let allProjects = [];
        try {
          const projectsRes = await getAllProjects();
          const projects = projectsRes.data || [];
          // Fetch members and tasks for each project
          allProjects = await Promise.all(
            projects.map(async (project, index) => {
              try {
                const [membersRes, tasksRes] = await Promise.all([
                  getProjectMembers(project.id || project._id),
                  getProjectTasks(project.id || project._id).catch((error) => {
                    console.warn(`Failed to fetch tasks for project ${project.id || project._id}:`, error.message);
                    return { data: [] };
                  })
                ]);
                
                // Kiểm tra trạng thái tất cả công việc trong dự án
                const projectTasks = tasksRes.data || [];
                const totalTasks = projectTasks.length;
                const completedTasks = projectTasks.filter(task => {
                  const status = String(task.status || '').toLowerCase();
                  const progress = task.progress || 0;
                  return status === 'completed' || 
                         status === 'hoàn thành' ||
                         progress >= 100;
                }).length;
                
                // Tính toán trạng thái dự án dựa trên công việc
                let calculatedStatus;
                let calculatedProgress = 0;
                
                if (totalTasks === 0) {
                  // Dự án không có công việc - sử dụng fallback từ project data
                  if (project.progress !== undefined && project.progress !== null) {
                    calculatedProgress = project.progress;
                    if (calculatedProgress >= 100) {
                      calculatedStatus = 'completed';
                    } else if (calculatedProgress > 0) {
                      calculatedStatus = 'in-progress';
                    } else {
                      calculatedStatus = 'waiting';
                    }
                  } else if (project.status) {
                    // Sử dụng status nếu không có progress
                    const status = String(project.status).toLowerCase();
                    if (status === 'completed' || status === 'hoàn thành') {
                      calculatedStatus = 'completed';
                      calculatedProgress = 100;
                    } else if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') {
                      calculatedStatus = 'in-progress';
                      calculatedProgress = 50;
                    } else if (status === 'review' || status === 'consider' || status === 'đang xem xét') {
                      calculatedStatus = 'review';
                      calculatedProgress = 75;
                    } else {
                      calculatedStatus = 'waiting';
                      calculatedProgress = 0;
                    }
                  } else {
                    calculatedStatus = 'waiting';
                    calculatedProgress = 0;
                  }
                } else if (completedTasks === totalTasks && totalTasks > 0) {
                  // Tất cả công việc đã hoàn thành
                  calculatedStatus = 'completed';
                  calculatedProgress = 100;
                } else if (completedTasks > 0 && completedTasks < totalTasks) {
                  // Một số công việc đã hoàn thành
                  calculatedStatus = 'in-progress';
                  calculatedProgress = Math.round((completedTasks / totalTasks) * 100);
                } else {
                  // Chưa có công việc nào hoàn thành
                  calculatedStatus = 'waiting';
                  calculatedProgress = 0;
                }
               
                return {
                  ...project,
                  status: calculatedStatus,
                  progress: calculatedProgress,
                  members: membersRes.data || [],
                };
              } catch {
                const fallbackStatus = 'waiting';
                const fallbackProgress = fallbackStatus === 'completed' ? 100 : 
                                      fallbackStatus === 'in-progress' ? 50 : 
                                      fallbackStatus === 'review' ? 75 : 0;
                return {
                  ...project,
                  status: fallbackStatus,
                  progress: fallbackProgress,
                  members: [],
                };
              }
            })
          );
        } catch {
          allProjects = [];
        }
        setProjects(allProjects);
        projectsCache = allProjects;
        cacheTimestamp = Date.now();
        setLoading(false);
      }
      refetchProjects();
    };

    const handleTaskUpdate = () => {
      // Clear cache to force refresh
      projectsCache = null;
      cacheTimestamp = 0;
      preloadPromise = null;
      // Refetch projects
      async function refetchProjects() {
        const now = Date.now();
        setLoading(true);
        let allProjects = [];
        try {
          const projectsRes = await getAllProjects();
          const projects = projectsRes.data || [];
          // Fetch members and tasks for each project
          allProjects = await Promise.all(
            projects.map(async (project, index) => {
              try {
                const [membersRes, tasksRes] = await Promise.all([
                  getProjectMembers(project.id || project._id),
                  getProjectTasks(project.id || project._id).catch((error) => {
                    console.warn(`Failed to fetch tasks for project ${project.id || project._id}:`, error.message);
                    return { data: [] };
                  })
                ]);
                
                // Kiểm tra trạng thái tất cả công việc trong dự án
                const projectTasks = tasksRes.data || [];
                const totalTasks = projectTasks.length;
                const completedTasks = projectTasks.filter(task => {
                  const status = String(task.status || '').toLowerCase();
                  const progress = task.progress || 0;
                  return status === 'completed' || 
                         status === 'hoàn thành' ||
                         progress >= 100;
                }).length;
                
                // Tính toán trạng thái dự án dựa trên công việc
                let calculatedStatus;
                let calculatedProgress = 0;
                
                if (totalTasks === 0) {
                  // Dự án không có công việc - sử dụng fallback từ project data
                  if (project.progress !== undefined && project.progress !== null) {
                    calculatedProgress = project.progress;
                    if (calculatedProgress >= 100) {
                      calculatedStatus = 'completed';
                    } else if (calculatedProgress > 0) {
                      calculatedStatus = 'in-progress';
                    } else {
                      calculatedStatus = 'waiting';
                    }
                  } else if (project.status) {
                    // Sử dụng status nếu không có progress
                    const status = String(project.status).toLowerCase();
                    if (status === 'completed' || status === 'hoàn thành') {
                      calculatedStatus = 'completed';
                      calculatedProgress = 100;
                    } else if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') {
                      calculatedStatus = 'in-progress';
                      calculatedProgress = 50;
                    } else if (status === 'review' || status === 'consider' || status === 'đang xem xét') {
                      calculatedStatus = 'review';
                      calculatedProgress = 75;
                    } else {
                      calculatedStatus = 'waiting';
                      calculatedProgress = 0;
                    }
                  } else {
                    calculatedStatus = 'waiting';
                    calculatedProgress = 0;
                  }
                } else if (completedTasks === totalTasks && totalTasks > 0) {
                  // Tất cả công việc đã hoàn thành
                  calculatedStatus = 'completed';
                  calculatedProgress = 100;
                } else if (completedTasks > 0 && completedTasks < totalTasks) {
                  // Một số công việc đã hoàn thành
                  calculatedStatus = 'in-progress';
                  calculatedProgress = Math.round((completedTasks / totalTasks) * 100);
                } else {
                  // Chưa có công việc nào hoàn thành
                  calculatedStatus = 'waiting';
                  calculatedProgress = 0;
                }
               
                return {
                  ...project,
                  status: calculatedStatus,
                  progress: calculatedProgress,
                  members: membersRes.data || [],
                };
              } catch {
                const fallbackStatus = 'waiting';
                const fallbackProgress = fallbackStatus === 'completed' ? 100 : 
                                      fallbackStatus === 'in-progress' ? 50 : 
                                      fallbackStatus === 'review' ? 75 : 0;
                return {
                  ...project,
                  status: fallbackStatus,
                  progress: fallbackProgress,
                  members: [],
                };
              }
            })
          );
        } catch {
          allProjects = [];
        }
        setProjects(allProjects);
        projectsCache = allProjects;
        cacheTimestamp = Date.now();
        setLoading(false);
      }
      refetchProjects();
    };

    window.addEventListener('projectUpdated', handleProjectUpdate);
    window.addEventListener('taskUpdated', handleTaskUpdate);

    return () => {
      window.removeEventListener('projectUpdated', handleProjectUpdate);
      window.removeEventListener('taskUpdated', handleTaskUpdate);
    };
  }, []);

  const handleFilter = () => {
    // Filter functionality
  };
  const handleExport = (type) => {
    if (type === "excel") {
      const exportData = projects.map(({ id, name, startDate, endDate, status }) => ({
        "Mã dự án": id,
        "Tên dự án": name,
        "Thời gian bắt đầu": startDate,
        "Thời gian kết thúc": endDate,
        "Trạng thái":
          status === 'completed' ? 'Hoàn thành' :
          status === 'in-progress' ? 'Đang triển khai' :
          status === 'in_progress' ? 'Đang triển khai' :
          status === 'waiting' ? 'Đang chờ' :
          status === 'pending' ? 'Đang chờ' :
          status === 'overdue' ? 'Quá hạn' :
          status === 'review' ? 'Đang xem xét' :
          status === 'consider' ? 'Đang xem xét' :
          status === 'Hoàn thành' ? 'Hoàn thành' :
          status === 'Đang triển khai' ? 'Đang triển khai' :
          status === 'Đang chờ' ? 'Đang chờ' :
          status === 'Quá hạn' ? 'Quá hạn' :
          status === 'Xem xét' ? 'Xem xét' : status,
      }));
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "DanhSachDuAn");
      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      saveAs(new Blob([wbout], { type: "application/octet-stream" }), "danh_sach_du_an.xlsx");
    } else if (type === "pdf") {
      const doc = new jsPDF();
      doc.text("Danh sách dự án", 10, 10);
      let y = 20;
      projects.forEach((project, idx) => {
        doc.text(`${idx + 1}. ${project.name} (${project.id})`, 10, y);
        doc.text(`Thời gian: ${formatDate(project.startDate)} - ${formatDate(project.endDate)}`, 10, y + 8);
        doc.text(`Trạng thái: ${project.status}`, 10, y + 16);
        y += 26;
        if (y > 270) { doc.addPage(); y = 20; }
      });
      doc.save("danh_sach_du_an.pdf");
    }
  };

  const handleDelete = (project) => {
    setDeleteProject(project);
  };

  const handleConfirmDelete = () => {
    if (deleteProject) {
      setProjects((prevProjects) => prevProjects.filter((project) => project.id !== deleteProject.id));
    }
    setDeleteProject(null);
  };

  const handleCancelDelete = () => {
    setDeleteProject(null);
  };

  const handleUpdateProject = (updatedProject) => {
    setProjects((prevProjects) =>
      prevProjects.map((project) =>
        project.id === updatedProject.id ? { ...project, ...updatedProject } : project
      )
    );
    setEditProject(null);
  };

  return (
    <div className="statistical-list-container">
      <StatisticalCard
        fileIcon={fileIcon}
        chartIcon={chartIcon}
        userGroupIcon={userGroupIcon}
        filterIcon={filterIcon}
        downloadIcon={downloadIcon}
        onFilter={handleFilter}
        onExport={handleExport}
        projectsData={projects}
      />
      <div className="project-list">
        {loading ? (
          <>
            {[1,2,3].map(i => (
              <div key={i} className="project-card" style={{ opacity: 0.7 }}>
                <div className="project-header">
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start", width: "100%" }}>
                    <div>
                      <div style={{ width: '180px', height: '20px', background: '#f0f0f0', borderRadius: '4px', marginBottom: '8px', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: '120px', height: '14px', background: '#f0f0f0', borderRadius: '4px', marginBottom: '8px', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: '150px', height: '14px', background: '#f0f0f0', borderRadius: '4px', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                    <div style={{ display: "flex", gap: 40 }}>
                      <div className="project-members">
                        <div>Thành viên</div>
                        <div className="avatars">
                          {[1,2,3].map(j => (
                            <span key={j} className="avatar">
                              <div style={{ width: 24, height: 24, borderRadius: "50%", background: "#f0f0f0", animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="project-status">
                        <div>Trạng thái</div>
                        <div style={{ display: "flex", alignItems: "center", gap: 6 }}>
                          <div style={{ width: 18, height: 18, background: '#f0f0f0', borderRadius: '4px', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                          <div style={{ width: '80px', height: '14px', background: '#f0f0f0', borderRadius: '4px', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                        </div>
                      </div>
                    </div>
                    <div className="project-actions">
                      <div style={{ width: 18, height: 18, background: '#f0f0f0', borderRadius: '4px', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                  </div>
                </div>
                <div className="project-progress">
                  <div className="progress-label">
                    <div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                  </div>
                  <div className="progress-bar">
                    <div className="progress-bar-inner" style={{ width: `${Math.random() * 100}%`, background: '#f0f0f0', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                  </div>
                  <div style={{ width: '80px', height: '14px', background: '#f0f0f0', borderRadius: '4px', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                </div>
              </div>
            ))}
            <style>{`
              @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
              }
            `}</style>
          </>
        ) : error ? (
          <div style={{ color: 'red' }}>{error}</div>
        ) : projects.length === 0 ? (
          <div>Không có dữ liệu dự án.</div>
        ) : (
          projects.map((project) => (
            <div className="project-card" key={project.id || project._id}>
              <div className="project-header">
                <div style={{
                  display: "flex",
                  alignItems: "flex-start",
                  width: "100%"
                }}>
                  <div style={{ flex: 2, minWidth: 180 }}>
                    <div className="project-name">{project.name}</div>
                    <div className="project-code">Mã dự án: {project.projectCode || `PRJ-${(project.id || project._id || '').toString().slice(-6)}`}</div>
                    <div className="project-time">
                      <img src={timeIcon} alt="time" style={{ width: 14, marginRight: 4, marginBottom: -2 }} />
                      Thời gian<br />
                      {formatDate(project.startDate)} - {formatDate(project.endDate)}
                    </div>
                  </div>
                  <div style={{ flex: 1, minWidth: 160, display: "flex", alignItems: "center", justifyContent: "center" }}>
                    <div className="project-members">
                      <div>Thành viên</div>
                      <div className="avatars" style={{ display: 'flex', alignItems: 'center', minHeight: 32 }}>
                        {(project.members && project.members.length > 0) ? (
                          project.members.map((u, idx) => {
                            const memberName = u.user?.fullName || u.fullName || u.name || 'Thành viên';
                            const memberEmail = u.user?.email || u.email || '';
                            const avatar = u.user?.avatar && u.user.avatar.trim() !== ''
                              ? u.user.avatar
                              : (u.avatar && u.avatar.trim() !== ''
                              ? u.avatar
                                : `https://ui-avatars.com/api/?name=${encodeURIComponent(memberName)}&background=007bff&color=fff`);



                            return (
                              <div
                                key={u.membershipId || u.id || u._id || memberName + idx}
                                style={{
                                  position: 'relative',
                                  display: 'inline-block',
                                  marginLeft: idx > 0 ? -8 : 0,
                                }}
                              >
                                <img
                                src={avatar}
                                alt={memberName}
                                title={`${memberName}${memberEmail ? ` - ${memberEmail}` : ''}`}
                                style={{
                                  width: 24,
                                  height: 24,
                                  borderRadius: "50%",
                                  objectFit: "cover",
                                  border: '2px solid white',
                                  cursor: 'pointer'
                                }}
                                onError={(e) => {
                                  e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(memberName)}&background=007bff&color=fff`;
                                }}
                                  onMouseEnter={(e) => {
                                    const tooltip = document.createElement('div');
                                    tooltip.textContent = `${memberName}${memberEmail ? ` - ${memberEmail}` : ''}`;
                                    tooltip.style.cssText = `
                                      position: absolute;
                                      background: rgba(0,0,0,0.8);
                                      color: white;
                                      padding: 4px 8px;
                                      border-radius: 4px;
                                      font-size: 12px;
                                      z-index: 1000;
                                      white-space: nowrap;
                                      pointer-events: none;
                                      top: -30px;
                                      left: 50%;
                                      transform: translateX(-50%);
                                    `;
                                    tooltip.id = 'custom-tooltip';
                                    e.target.parentNode.appendChild(tooltip);
                                  }}
                                  onMouseLeave={() => {
                                    const tooltip = document.getElementById('custom-tooltip');
                                    if (tooltip) tooltip.remove();
                                  }}
                              />
                              </div>
                            );
                          })
                        ) : (
                          <span className="no-member" style={{ color: '#5d5d5d', fontStyle: 'italic', fontSize: 14 }}>
                            Chưa có
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div style={{ flex: 1, minWidth: 120, display: "flex", alignItems: "center", justifyContent: "center" }}>
                    <div className="project-status">
                      <div>Trạng thái</div>
                      <div style={{ display: "flex", alignItems: "center", gap: 6 }}>
                        <img src={getStatusIcon(project.status)} alt={project.status} style={{ width: 18, marginBottom: -3, display: "inline-block", verticalAlign: "middle" }} />
                        <span className="status-text">
                          {(() => {
                            const status = String(project.status || '').toLowerCase();
                            switch (status) {
                              case 'completed':
                              case 'hoàn thành':
                                return 'Hoàn thành';
                              case 'in-progress':
                              case 'in_progress':
                              case 'đang triển khai':
                                return 'Đang triển khai';
                              case 'waiting':
                              case 'pending':
                              case 'đang chờ':
                                return 'Đang chờ';
                              case 'overdue':
                              case 'quá hạn':
                                return 'Quá hạn';
                              case 'review':
                              case 'consider':
                              case 'đang xem xét':
                                return 'Đang xem xét';
                              default:
                                return 'Đang chờ';
                            }
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div style={{ flex: 0, minWidth: 40, display: "flex", alignItems: "center", justifyContent: "center" }}>
                    <div className="project-actions">
                      <button className="btn-view" onClick={() => setSelectedProject(project)}>
                        <img src={eyeIcon} alt="Xem" style={{ width: 18 }} />
                      </button>
                      {/* <button className="btn-edit" onClick={() => setEditProject(project)}>
                        <img src={editIcon} alt="Sửa" style={{ width: 18 }} />
                      </button> */}
                    </div>
                  </div>
                </div>
              </div>
              <div className="project-progress">
                <div className="progress-label">
                  <img src={jobIcon} alt="job" style={{ width: 14, marginRight: 4, marginBottom: -2 }} />
                  Tiến độ
                </div>
                <div className="progress-bar">
                  <div className="progress-bar-inner" style={{ width: `${(() => {
                    const status = String(project.status || '').toLowerCase();
                    if (status === 'completed' || status === 'hoàn thành') return 100;
                    if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') return 50;
                    if (status === 'review' || status === 'consider' || status === 'đang xem xét') return 75;
                    return project.progress || 0;
                  })()}%` }}></div>
                </div>
                <div className="progress-desc">
                  {(() => {
                    const status = String(project.status || '').toLowerCase();
                    if (status === 'completed' || status === 'hoàn thành') return '100/100% hoàn thành';
                    if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') return '50/100% hoàn thành';
                    if (status === 'review' || status === 'consider' || status === 'đang xem xét') return '75/100% hoàn thành';
                    return `${project.progress || 0}/100% hoàn thành`;
                  })()}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      {selectedProject && (
        <DetailJob
          task={{
            id: selectedProject.id || selectedProject._id,
            name: selectedProject.name,
            projectId: selectedProject.id || selectedProject._id,
            // Không gọi API comments/attachments cho project detail
            _skipComments: true,
            _skipAttachments: true,
            startDate: selectedProject.startDate,
            endDate: selectedProject.endDate,
            dueDate: selectedProject.endDate, // Thêm dueDate cho JobUpdate
            createdBy: (() => {
              // Ưu tiên lấy thông tin từ selectedProject.createdBy
              if (selectedProject.createdBy) {
                return {
                  fullName: selectedProject.createdBy.fullName || selectedProject.createdBy.name || 'Người tạo',
                  name: selectedProject.createdBy.fullName || selectedProject.createdBy.name || 'Người tạo',
                  avatar: selectedProject.createdBy.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(selectedProject.createdBy.fullName || selectedProject.createdBy.name || 'U')
                };
              }

              // Fallback: lấy thông tin user đang đăng nhập
              const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
              const user = currentUser.user || currentUser;

              return {
                fullName: user.fullName || user.name || 'Người dùng hiện tại',
                name: user.fullName || user.name || 'Người dùng hiện tại',
                avatar: user.avatar || 'https://ui-avatars.com/api/?name=?'
              };
            })(),
            assignee: (selectedProject.members || []).map(mem => ({
              id: mem.membershipId || mem.id || mem._id,
              userId: mem.user?.id || mem.user?._id || mem.id || mem._id, // Thêm userId cho JobUpdate
              fullName: mem.user?.fullName || mem.fullName || mem.name || 'Thành viên',
              name: mem.user?.fullName || mem.fullName || mem.name || 'Thành viên',
              email: mem.user?.email || mem.email || '',
              avatar: mem.user?.avatar && mem.user.avatar.trim() !== ''
                ? mem.user.avatar
                : (mem.avatar && mem.avatar.trim() !== ''
                ? mem.avatar
                  : 'https://ui-avatars.com/api/?name=' + encodeURIComponent(mem.user?.fullName || mem.fullName || mem.name || 'U'))
            })),
            assignedTo: (selectedProject.members || []).map(mem => ({
              id: mem.membershipId || mem.id || mem._id,
              userId: mem.user?.id || mem.user?._id || mem.id || mem._id, // Thêm userId cho JobUpdate
              fullName: mem.user?.fullName || mem.fullName || mem.name || 'Thành viên',
              name: mem.user?.fullName || mem.fullName || mem.name || 'Thành viên',
              email: mem.user?.email || mem.email || '',
              avatar: mem.user?.avatar && mem.user.avatar.trim() !== ''
                ? mem.user.avatar
                : (mem.avatar && mem.avatar.trim() !== ''
                ? mem.avatar
                  : 'https://ui-avatars.com/api/?name=' + encodeURIComponent(mem.user?.fullName || mem.fullName || mem.name || 'U'))
            })),
            priority: (() => {
              const priority = String(selectedProject.priority || '').toLowerCase();
              // Map backend priority to frontend priority
              const priorityMapping = {
                'low': 'low',
                'medium': 'medium',
                'high': 'high', 
                'urgent': 'critical',
                'critical': 'critical'
              };
              return priorityMapping[priority] || 'medium';
            })(),
            status: (() => {
              const status = String(selectedProject.status || '').toLowerCase();
              switch (status) {
                case 'completed':
                case 'hoàn thành':
                  return 'completed';
                case 'in-progress':
                case 'in_progress':
                case 'đang triển khai':
                  return 'in_progress';
                case 'waiting':
                case 'pending':
                case 'đang chờ':
                  return 'waiting';
                case 'overdue':
                case 'quá hạn':
                  return 'overdue';
                case 'review':
                case 'consider':
                case 'đang xem xét':
                  return 'review';
                default:
                  return 'waiting';
              }
            })(),
            description: selectedProject.description || 'Không có mô tả',
            progress: (() => {
              const status = String(selectedProject.status || '').toLowerCase();
              if (status === 'completed' || status === 'hoàn thành') return 100;
              if (status === 'in-progress' || status === 'in_progress' || status === 'đang triển khai') return 50;
              if (status === 'review' || status === 'consider' || status === 'đang xem xét') return 75;
              return selectedProject.progress || 0;
            })(),
            projectCode: selectedProject.projectCode || `PRJ-${(selectedProject.id || selectedProject._id || '').toString().slice(-6)}`,
          }}
          onClose={() => setSelectedProject(null)}
          hideActivityAndComment={true}
          hideEditButton={true}
        />
      )}
      {deleteProject && (
        <DeleteProjectForm
          projectName={deleteProject.name}
          onDelete={handleConfirmDelete}
          onClose={handleCancelDelete}
        />
      )}
      {editProject && (
        <StatisticalUpdate
          project={editProject}
          onClose={() => setEditProject(null)}
          onUpdate={handleUpdateProject}
        />
      )}
    </div>
  );
};

export default List;
