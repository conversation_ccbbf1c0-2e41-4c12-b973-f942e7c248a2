import { useCallback, useEffect, useRef, useState } from 'react';
import { getAllDepartments, transformDepartmentData } from "../../api/departmentManagement";
import { getProjectById, updateProject } from "../../api/projectManagement";
import { getAllUsers, transformUserData } from "../../api/userManagement";
import addIcon from "../../assets/add.svg";
import closeIcon from "../../assets/closeLoc.svg";
import startDateIcon from "../../assets/creationdate.svg";
import EditIcon from "../../assets/edit.svg";
import endDateIcon from "../../assets/enddate.svg";
import eyeIcon from "../../assets/eye.svg";
import fileTextIcon from "../../assets/file-text.svg";
import TrashIcon from "../../assets/trash.svg";
import { showError, showSuccess } from "../../components/Toastify";
import "../../styles/ProjectDetailPopup.css";
import DetailJob from "./JobDetail";
import MemberAddPopup from "./MemberAddPopup";

// Custom Select Dropdown Component
const CustomSelect = ({ options, value, onChange, placeholder, disabled, error }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const selectedOption = options.find(option => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (option) => {
    onChange(option.value);
    setIsOpen(false);
  };

  return (
    <div className="custom-select" ref={dropdownRef}>
      <div 
        className={`project-form-group select ${error ? 'error' : ''}`} 
        onClick={toggleDropdown}
        style={{ 
          position: 'relative',
          cursor: disabled ? 'not-allowed' : 'pointer',
          opacity: disabled ? 0.7 : 1
        }}
      >
        <div className="select-selected" style={{
          padding: '6px 8px',
          border: '1px solid #e0e0e0',
          borderRadius: '6px',
          fontSize: '12px',
          backgroundColor: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '32px',
          boxSizing: 'border-box'
        }}>
          <span style={{ color: selectedOption ? '#5B5B5B' : '#999' }}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <svg 
            width="12" 
            height="8" 
            viewBox="0 0 12 8" 
            fill="none"
            style={{ 
              transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s'
            }}
          >
            <path d="M1 1L6 6L11 1" stroke="#999" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        {isOpen && (
          <div className="select-items" style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            backgroundColor: '#fff',
            border: '1px solid #e0e0e0',
            borderTop: 'none',
            borderRadius: '0 0 6px 6px',
            maxHeight: '120px',
            overflowY: 'auto',
            zIndex: 1000,
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            {options.map((option) => (
              <div
                key={option.value}
                onClick={() => handleSelect(option)}
                style={{
                  padding: '6px 8px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  backgroundColor: selectedOption?.value === option.value ? '#f0f0f0' : '#fff'
                }}
                onMouseOver={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                onMouseOut={(e) => e.target.style.backgroundColor = selectedOption?.value === option.value ? '#f0f0f0' : '#fff'}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Component form sửa dự án đầy đủ
const EditProjectForm = ({ projectName, projectId, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    name: projectName || "",
    description: "",
    startDate: "",
    endDate: "",
    departmentId: "",
    leaderId: "",
    priority: "medium",
    members: [],
    followers: [],
    attachments: []
  });
  const [departments, setDepartments] = useState([]);
  const [users, setUsers] = useState([]);
  const [showMemberPopup, setShowMemberPopup] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState("");
  const [fieldErrors, setFieldErrors] = useState({});

  // Load project data và các dependencies
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingData(true);
        
        // Load project details, users, departments
        const [projectRes, usersRes, deptsRes] = await Promise.all([
          projectId ? getProjectById(projectId) : null,
          getAllUsers({ populate: 'department,departmentId', include: 'department' }),
          getAllDepartments()
        ]);

        // Load users
        if (usersRes.success) {
          const transformedUsers = (usersRes.data || []).map(transformUserData);
          setUsers(transformedUsers);
        }

        // Load departments
        if (deptsRes.success) {
          const transformedDepts = (deptsRes.data || []).map(transformDepartmentData);
          setDepartments(transformedDepts);
        }

        // Load project data if editing
        if (projectRes && projectRes.success) {
          const project = projectRes.data;
          
          // Load project members từ thông tin project trả về
          let projectMembers = [];
          try {
            // Nếu project có members từ populate
            if (project.members && Array.isArray(project.members)) {
              projectMembers = project.members.map(member => ({
                id: member._id || member.id,
                name: member.fullName || member.name,
                email: member.email,
                avatar: member.avatar || '/default-avatar.png'
              }));
            } else {
              // Fallback: gọi API riêng để load members nếu cần
              // Trying to load members from API
              const { getAllUsers } = await import('../../api/userManagement');
              const usersResponse = await getAllUsers();
              if (usersResponse.success) {
                // Filter users thuộc về project này (giả sử có projectIds trong user)
                // Hoặc có thể dùng cách khác tùy theo backend structure
                // Loaded users, but need to filter by project membership
              }
            }
          } catch (err) {
            console.error('Error loading project members:', err);
          }
          
                  // Loaded project data and members
          
          setFormData({
            name: project.name || "",
            description: project.description || "",
            startDate: project.startDate ? project.startDate.split('T')[0] : "",
            endDate: project.endDate ? project.endDate.split('T')[0] : "",
            departmentId: project.departmentId?._id || project.departmentId || "",
            leaderId: project.leaderId?._id || project.leaderId || "",
            priority: project.priority || "medium",
            members: projectMembers,
            followers: project.followers || [],
            attachments: project.attachments || []
          });
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Có lỗi xảy ra khi tải dữ liệu');
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [projectId]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError("");
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (loading) return;

    // Validation
    const newFieldErrors = {};
    if (!formData.name?.trim()) {
      newFieldErrors.name = "Vui lòng nhập tên dự án";
    }
    if (!formData.description?.trim()) {
      newFieldErrors.description = "Vui lòng nhập mô tả dự án";
    }
    if (!formData.departmentId) {
      newFieldErrors.departmentId = "Vui lòng chọn phòng ban";
    }
    if (!formData.leaderId) {
      newFieldErrors.leaderId = "Vui lòng chọn trưởng dự án";
    }
    
    // Validation cho dates (nếu có nhập)
    const today = new Date().toISOString().split('T')[0]; // Format YYYY-MM-DD
    if (formData.startDate && formData.startDate < today) {
      newFieldErrors.startDate = "Thời gian bắt đầu không thể là quá khứ";
    }
    if (formData.endDate && formData.endDate < today) {
      newFieldErrors.endDate = "Thời gian kết thúc không thể là quá khứ";
    }
    if (formData.startDate && formData.endDate && new Date(formData.startDate) > new Date(formData.endDate)) {
      newFieldErrors.endDate = "Thời gian kết thúc phải sau thời gian bắt đầu";
    }
    
    // Không bắt buộc members cho update (có thể sửa sau)

    if (Object.keys(newFieldErrors).length > 0) {
      setFieldErrors(newFieldErrors);
      return;
    }

    try {
      setLoading(true);
      setError("");

      const updateData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        startDate: formData.startDate || null,
        endDate: formData.endDate || null,
        departmentId: formData.departmentId,
        leaderId: formData.leaderId,
        priority: formData.priority || "medium",
      };

      // Sending update data
      const response = await updateProject(projectId, updateData);
              // Update response received
      
      if (response.success) {
        showSuccess('Cập nhật dự án thành công!');
                  // Project updated successfully, calling onSave
        if (onSave) {
          onSave(updateData);
        }
        onClose();
      } else {
        console.error('Update failed:', response);
        setError(response.message || "Cập nhật dự án thất bại");
        showError(response.message || "Cập nhật dự án thất bại");
      }
    } catch (error) {
      console.error('Error updating project:', error);
      setError(error.message || "Có lỗi xảy ra khi cập nhật dự án");
    } finally {
      setLoading(false);
    }
  };

  // Lọc users theo department cho leader selection
  const filteredLeaders = formData.departmentId
    ? users.filter(user => user.departmentId === formData.departmentId)
    : users;

  const leaderOptions = [
    { value: "", label: "Chọn trưởng dự án" },
    ...filteredLeaders.map(user => ({
      value: user.id, 
      label: user.name
    }))
  ];

  const departmentOptions = [
    { value: "", label: "Chọn phòng ban" },
    ...departments.map(dept => ({ value: dept.id, label: dept.name }))
  ];

  const priorityOptions = [
    { value: "low", label: "Thấp" },
    { value: "medium", label: "Trung bình" },
    { value: "high", label: "Cao" },
    { value: "critical", label: "Khẩn cấp" },
  ];

  if (loadingData) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0,0,0,0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div className="project-create-container" style={{ background: 'white', borderRadius: '8px', maxHeight: '90vh', overflowY: 'auto' }}>
          <div style={{ padding: '40px', textAlign: 'center' }}>
            <div>Đang tải dữ liệu...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }} onClick={onClose}>
      <div className="project-create-container" style={{ background: 'white', borderRadius: '8px', maxHeight: '90vh', overflowY: 'auto' }} onClick={e => e.stopPropagation()}>
      <div className="project-create-header">
        <h2>Sửa dự án</h2>
        <button className="project-close-btn" onClick={onClose}>
          <img src={closeIcon} alt="Đóng" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="project-create-form" noValidate>
        {/* Error message */}
        {error && (
          <div className="project-error-message" style={{
            color: '#dc3545',
            backgroundColor: '#f8d7da',
            border: '1px solid #f5c6cb',
            borderRadius: '4px',
            padding: '8px 12px',
            marginBottom: '16px',
            fontSize: '14px',
            whiteSpace: 'pre-wrap'
          }}>
            {error}
          </div>
        )}

        <div className="project-form-group">
          <label>Tên dự án <span className="required-asterisk">*</span></label>
          <input
            type="text"
            placeholder="Hệ thống quản lý bán hàng"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={fieldErrors.name ? 'error' : ''}
          />
          <div className="field-error">{fieldErrors.name}</div>
        </div>

        <div className="project-form-group">
          <label>Mô tả <span className="required-asterisk">*</span></label>
          <textarea
            placeholder="Phát triển hệ thống quản lý khách hàng"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className={fieldErrors.description ? 'error' : ''}
          />
          <div className="field-error">{fieldErrors.description}</div>
        </div>

        <div className="project-form-row">
          <div className="project-form-group">
            <label>Thời gian bắt đầu <span className="required-asterisk">*</span></label>
            <div className={`project-date-input-group ${fieldErrors.startDate ? 'error' : ''}`} onClick={() => document.querySelector('input[name="editProjectStartDate"]').showPicker?.()}>
              <img src={startDateIcon} alt="calendar" className="project-calendar-icon" />
              <input
                name="editProjectStartDate"
                type="date"
                value={formData.startDate}
                min={new Date().toISOString().split('T')[0]}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                placeholder="Chọn ngày bắt đầu"
              />
            </div>
            <div className="field-error">{fieldErrors.startDate}</div>
          </div>
          <div className="project-form-group">
            <label>Thời gian kết thúc <span className="required-asterisk">*</span></label>
            <div className={`project-date-input-group ${fieldErrors.endDate ? 'error' : ''}`} onClick={() => document.querySelector('input[name="editProjectEndDate"]').showPicker?.()}>
              <img src={endDateIcon} alt="calendar" className="project-calendar-icon" />
              <input
                name="editProjectEndDate"
                type="date"
                value={formData.endDate}
                min={new Date().toISOString().split('T')[0]}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                placeholder="Chọn ngày kết thúc"
              />
            </div>
            <div className="field-error">{fieldErrors.endDate}</div>
          </div>
        </div>

        <div className="project-form-row project-three-columns">
          <div className="project-form-group">
            <label>Phòng ban <span className="required-asterisk">*</span></label>
            <CustomSelect
              options={departmentOptions}
              value={formData.departmentId}
              onChange={(value) => handleInputChange('departmentId', value)}
              placeholder="Chọn phòng ban"
              disabled={false}
              error={fieldErrors.departmentId}
            />
            <div className="field-error">{fieldErrors.departmentId}</div>
          </div>
          <div className="project-form-group">
            <label>Trưởng dự án <span className="required-asterisk">*</span></label>
            <CustomSelect
              options={leaderOptions}
              value={formData.leaderId}
              onChange={(value) => handleInputChange('leaderId', value)}
              placeholder="Chọn trưởng dự án"
              disabled={false}
              error={fieldErrors.leaderId}
            />
            <div className="field-error">{fieldErrors.leaderId}</div>
          </div>
          <div className="project-form-group">
            <label>Mức độ ưu tiên</label>
            <CustomSelect
              options={priorityOptions}
              value={formData.priority}
              onChange={(value) => handleInputChange('priority', value)}
              placeholder="Chọn mức độ ưu tiên"
            />
          </div>
        </div>

        <div className="project-members-files-row">
          <div className="project-form-group">
            <label>Thành viên thực hiện</label>
            <div className="project-members-section">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
                <button type="button" className="project-add-member-btn" onClick={() => setShowMemberPopup(true)} style={{ width: '24px', height: '24px', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 0, border: '1px solid #fff', background: '#fff' }}>
                  <img src={addIcon} alt="Add member" style={{ width: '16px', height: '16px' }} />
                </button>
                {formData.members.map((member, index) => (
                  <div key={index} className="project-members-avatar" title={`${member.name} - ${member.email}`} style={{ width: '24px', height: '24px', borderRadius: '50%', overflow: 'visible', position: 'relative', marginRight: '6px' }}>
                    <img src={member.avatar || '/default-avatar.png'} alt={member.name} style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }} />
          <button
                      type="button"
                      className="project-remove-member-btn"
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          members: prev.members.filter((_, i) => i !== index)
                        }));
                      }}
                      title="Xóa thành viên"
                      style={{ position: 'absolute', top: '-5px', right: '-5px', width: '14px', height: '14px', borderRadius: '50%', border: 'none', background: '#ff4757', color: 'white', fontSize: '9px', fontWeight: 'bold', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 10 }}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <div className="field-error">{fieldErrors.members}</div>
            </div>
          </div>

          <div className="project-form-group">
            <label>Tệp đính kèm</label>
            <div className="project-file-items">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <button type="button" className="project-add-file-btn" onClick={() => {}} style={{ width: '24px', height: '24px', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 0, border: '1px solid #fff', background: '#fff' }}>
                  <img src={addIcon} alt="Add file" style={{ width: '16px', height: '16px' }} />
          </button>
              </div>
              {formData.attachments && formData.attachments.length > 0 && (
                <div className="project-file-list">
                  {formData.attachments.map((file, index) => (
                    <div key={index} className="project-file-item">
                      <img src={fileTextIcon} alt="file" className="project-file-icon" />
                      <span className="project-file-name">{file.name || file}</span>
          <button
                        type="button"
                        className="project-file-remove"
            onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            attachments: prev.attachments.filter((_, i) => i !== index)
                          }));
                        }}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="project-form-action">
          <button 
            type="submit" 
            className="project-create-btn"
            disabled={loading}
            style={{
              opacity: loading ? 0.7 : 1,
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Đang cập nhật...' : 'Cập nhật dự án'}
          </button>
        </div>
      </form>

      {/* Popup thêm thành viên */}
      <MemberAddPopup
        isOpen={showMemberPopup}
        onClose={() => setShowMemberPopup(false)}
        onAddMember={(member) => {
          setFormData(prev => ({
            ...prev,
            members: [...prev.members, {
              id: member.id,
              name: member.name,
              email: member.email,
              avatar: member.avatar || '/default-avatar.png'
            }]
          }));
        }}
        existingMembers={formData.members}
        users={users}
        loadingUsers={false}
        departmentId={formData.departmentId}
        leaderId={formData.leaderId}
      />
      </div>
    </div>
  );
};

// Component form xác nhận xóa dự án
const DeleteProjectForm = ({ projectName, onClose, onDelete }) => (
  <div className="project-detail-modal-overlay" onClick={onClose}>
    <div className="project-detail-modal" onClick={e => e.stopPropagation()}>
      <h3 className="delete-project-form-title">
        Xoá dự án
      </h3>
      <p className="delete-project-form-description">
        Bạn có chắc chắn muốn xoá dự án {projectName} không? Hành động này không thể hoàn tác.
      </p>

      <div className="project-detail-modal-actions">
        <button
          onClick={onClose}
          className="project-detail-modal-btn cancel-btn"
        >
          Hủy
        </button>
        <button
          onClick={() => {
            onDelete();
            onClose();
          }}
          className="project-detail-modal-btn danger-btn"
        >
          Xoá dự án
        </button>
      </div>
    </div>
  </div>
);

// Component popup detail dự án chính
const ProjectDetailPopup = ({
  projectName,
  projectId,
  position,
  onClose,
  onEditProject,
  onDeleteProject,
  projectData // <-- nếu parent truyền vào, ưu tiên dùng, nếu không thì chỉ truyền name/id
}) => {
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteForm, setShowDeleteForm] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [detailProjectData, setDetailProjectData] = useState(projectData || null);

  // Load chi tiết project khi cần hiển thị detail
  const loadProjectDetail = useCallback(async () => {
    if (projectData) {
      // Nếu đã có projectData từ parent, sử dụng luôn
      setDetailProjectData(projectData);
      return;
    }

    if (!projectId) return;

    try {
      // Gọi API để lấy đầy đủ thông tin
      const response = await getProjectById(projectId);

      if (response.success) {
        const projectDetail = response.data;
        console.log('Project detail data:', projectDetail); // Debug log
        setDetailProjectData(projectDetail);
        } else {
          throw new Error('Cannot load project detail');
      }
    } catch (error) {
      console.error('Error loading project detail:', error);
      // Fallback với dữ liệu tối thiểu
      setDetailProjectData({
        id: projectId,
        name: projectName,
        description: 'Không thể tải thông tin chi tiết',
        members: [],
        leaderId: null,
        priority: 'medium',
        status: 'pending'
      });
    }
  }, [projectData, projectId, projectName]);

  // Load dữ liệu ngay khi component mount nếu chưa có projectData
  useEffect(() => {
    if (!projectData && projectId) {
      loadProjectDetail();
    }
  }, [projectId, projectData, loadProjectDetail]);

  const handleEditClick = () => {
    setShowEditForm(true);
    // Không gọi onClose() ở đây để popup detail vẫn hiển thị
  };

  const handleDeleteClick = () => {
    setShowDeleteForm(true);
    // Không gọi onClose() ở đây để popup detail vẫn hiển thị
  };

  const handleSaveProjectName = () => {
    // Không dùng logic cũ onEditProject nữa
    // Logic update đã được handle trong EditProjectForm
    setShowEditForm(false);
    onClose(); // Đóng toàn bộ popup sau khi lưu

    // Trigger refresh projects nếu có callback từ parent
    if (onEditProject) {
      onEditProject('refresh', null); // Signal để refresh projects
    }
  };

  const handleDeleteProject = () => {
    if (onDeleteProject) {
      onDeleteProject(projectName);
    }
    setShowDeleteForm(false);
    onClose(); // Đóng toàn bộ popup sau khi xóa
  };

  const handleViewDetail = () => {
    setShowDetail(true);
    // Dữ liệu đã được load sẵn trong useEffect, không cần load lại
  };

  // Chuẩn bị dữ liệu cho DetailJob
  const getDetailTask = () => {
    if (!detailProjectData) return null;

    const taskData = {
      id: detailProjectData.id || detailProjectData._id || projectId,
      name: detailProjectData.name || projectName,
      projectId: detailProjectData.id || detailProjectData._id || projectId,
      startDate: detailProjectData.startDate,
      endDate: detailProjectData.endDate,
      createdBy: (() => {
        console.log('=== DEBUG CREATED BY ===');
        console.log('detailProjectData:', detailProjectData);
        console.log('detailProjectData.createdBy:', detailProjectData.createdBy);
        console.log('detailProjectData.leaderId:', detailProjectData.leaderId);

        // Lấy thông tin user đang đăng nhập
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const user = currentUser.user || currentUser;
        console.log('Current user from localStorage:', user);

        // Nếu có thông tin createdBy từ project data
        if (detailProjectData.createdBy) {
          console.log('✅ Found createdBy in project data, using it');
          const createdBy = detailProjectData.createdBy;
          return {
            fullName: createdBy.fullName || createdBy.name || 'Người tạo',
            name: createdBy.fullName || createdBy.name || 'Người tạo',
            avatar: createdBy.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(createdBy.fullName || createdBy.name || 'U')
          };
        }

        // Fallback 1: Sử dụng thông tin leaderId nếu có và được populate
        if (detailProjectData.leaderId && typeof detailProjectData.leaderId === 'object') {
          console.log('✅ Found populated leaderId, using as creator');
          const leader = detailProjectData.leaderId;
          return {
            fullName: leader.fullName || leader.name || 'Leader',
            name: leader.fullName || leader.name || 'Leader',
            avatar: leader.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(leader.fullName || leader.name || 'L')
          };
        }

        // Tạm thời bỏ qua fallback 2 để tránh lỗi scope
        console.log('⚠️ Skipping leader lookup in users list due to scope issue');

        // Fallback cuối: sử dụng thông tin user hiện tại
        console.log('❌ No creator info found, using current user as fallback');
        return {
          fullName: user.fullName || user.name || 'Người dùng hiện tại',
          name: user.fullName || user.name || 'Người dùng hiện tại',
          avatar: user.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(user.fullName || user.name || 'Admin')
        };
      })(),
      assignedTo: (detailProjectData.members || []).map(mem => ({
        id: mem.membershipId || mem.id || mem._id,
        fullName: mem.user?.fullName || mem.fullName || mem.name || 'Thành viên',
        name: mem.user?.fullName || mem.fullName || mem.name || 'Thành viên',
        email: mem.user?.email || mem.email || '',
        avatar: mem.user?.avatar && mem.user.avatar.trim() !== ''
          ? mem.user.avatar
          : (mem.avatar && mem.avatar.trim() !== ''
          ? mem.avatar
            : 'https://ui-avatars.com/api/?name=' + encodeURIComponent(mem.user?.fullName || mem.fullName || mem.name || 'U'))
      })),
      priority: detailProjectData.priority || 'normal',
      status: detailProjectData.status === 'completed' ? 'Hoàn thành'
        : detailProjectData.status === 'in-progress' ? 'Đang triển khai'
        : detailProjectData.status === 'in_progress' ? 'Đang triển khai'
        : detailProjectData.status === 'waiting' ? 'Đang chờ'
        : detailProjectData.status === 'pending' ? 'Đang chờ'
        : detailProjectData.status === 'overdue' ? 'Quá hạn'
        : detailProjectData.status === 'review' ? 'Đang xem xét'
        : detailProjectData.status === 'consider' ? 'Đang xem xét'
        : (detailProjectData.status || 'Đang chờ'),
      description: detailProjectData.description || 'Không có mô tả',
      progress: detailProjectData.progress || (
        detailProjectData.status === 'completed' ? 100 :
        detailProjectData.status === 'in-progress' ? 50 :
        detailProjectData.status === 'in_progress' ? 50 :
        0
      ),
      projectCode: detailProjectData.projectCode || `PRJ-${(detailProjectData.id || detailProjectData._id || projectId || '').toString().slice(-6)}`,
    };

    return taskData;
  };

  return (
    <>
      {/* Form sửa dự án đầy đủ */}
      {showEditForm && (
        <EditProjectForm
          projectName={projectName}
          projectId={projectId}
          onClose={() => setShowEditForm(false)} // Quay lại popup detail
          onSave={handleSaveProjectName}
        />
      )}

      {/* Form xác nhận xóa dự án */}
      {showDeleteForm && (
        <DeleteProjectForm
          projectName={projectName}
          onClose={() => setShowDeleteForm(false)} // Quay lại popup detail
          onDelete={handleDeleteProject}
        />
      )}

      {/* Popup chi tiết dự án */}
      {showDetail && (
        <DetailJob
          task={getDetailTask()}
          onClose={() => setShowDetail(false)}
          hideActivityAndComment={true}
          hideEditButton={true}
        />
      )}

      {/* Popup detail chính - chỉ hiển thị khi không có form nào mở */}
      {!showEditForm && !showDeleteForm && !showDetail && (
        <div
          className="project-detail-popup"
          style={{
            top: position.top,
            left: position.left
          }}
          onClick={e => e.stopPropagation()}
        >
          <div
            className="project-detail-popup-option"
            onClick={handleViewDetail}
          >
            <img src={eyeIcon} alt="eye" className="project-detail-popup-icon" />
            Xem chi tiết
          </div>
          <div
            className="project-detail-popup-option"
            onClick={handleEditClick}
          >
            <img src={EditIcon} alt="edit" className="project-detail-popup-icon" />
            Sửa dự án
          </div>
          <div
            className="project-detail-popup-option delete-option"
            onClick={handleDeleteClick}
          >
            <img src={TrashIcon} alt="delete" className="project-detail-popup-icon delete-icon" />
            Xoá dự án
          </div>
        </div>
      )}
    </>
  );
};

export default ProjectDetailPopup;
export { DeleteProjectForm };

