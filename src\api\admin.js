import { ADMIN_ENDPOINTS, AUTH_ENDPOINTS } from './endpoints.js';

// Helper function để lấy token từ localStorage (sử dụng logic giống ProtectedRoute)
const getAuthToken = () => {
  try {
    // <PERSON><PERSON><PERSON> tra token từ nhiều nguồn khác nhau
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;

    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// Re-export universal profile functions for Admin role
export {
  getProfile,
  updateProfile,
  changePassword,
  getColleagues,
  getUserRole,
  getRoleDisplayName,
  getCurrentUserPermissions
} from './profile.js';

// ========== UPLOAD VÀ PROFILE MANAGEMENT ==========

// Upload avatar cho Admin
export async function uploadAvatar(file) {
  try {
    const formData = new FormData();
    formData.append('avatar', file);

    const token = getAuthToken();
    const response = await fetch(AUTH_ENDPOINTS.UPDATE_AVATAR, {
      method: 'POST',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
        // Không set Content-Type để browser tự động set với boundary cho FormData
      },
      body: formData,
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật profile Admin (bao gồm avatar)
export async function updateAdminProfile(profileData) {
  try {
    const response = await fetch(AUTH_ENDPOINTS.GET_PROFILE, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(profileData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy profile Admin
export async function getAdminProfile() {
  try {
    const response = await fetch(AUTH_ENDPOINTS.GET_PROFILE, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Đổi mật khẩu Admin
export async function changeAdminPassword(passwordData) {
  try {
    const response = await fetch(AUTH_ENDPOINTS.CHANGE_PASSWORD, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(passwordData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách đồng nghiệp cho Admin
export async function getAdminColleagues() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.COLLEAGUES, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== ADMIN-SPECIFIC FUNCTIONS ==========

// Lấy dashboard thống kê
export async function getDashboard() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.DASHBOARD, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy thống kê hệ thống
export async function getSystemStats() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.SYSTEM_STATS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách tất cả người dùng
export async function getAllUsers(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${ADMIN_ENDPOINTS.USERS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Tạo người dùng mới
export async function createUser(userData) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.CREATE_USER, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(userData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật thông tin người dùng
export async function updateUser(userId, userData) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.UPDATE_USER(userId), {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(userData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khóa/mở khóa tài khoản người dùng
export async function toggleUserBlock(userId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.TOGGLE_USER_BLOCK(userId), {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa người dùng
export async function deleteUser(userId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.DELETE_USER(userId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Thay đổi role người dùng
export async function changeUserRole(userId, newRole) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.CHANGE_USER_ROLE(userId), {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ newRole }),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khôi phục người dùng đã xóa
export async function restoreUser(userId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.RESTORE_USER(userId), {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách người dùng đã xóa
export async function getDeletedUsers() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.DELETED_USERS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy lịch sử hoạt động của người dùng
export async function getUserActivities(userId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.USER_ACTIVITIES(userId), {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy tất cả hoạt động hệ thống
export async function getAllActivities() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.ALL_ACTIVITIES, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== QUẢN LÝ DỰ ÁN ==========

// Lấy tất cả dự án
export async function getAllProjects(params = {}) {
  try {
    // Import transform function
    const { transformProjectData } = await import('./projectManagement.js');

    // Mặc định lấy tất cả dự án
    const defaultParams = { limit: 1000, ...params };
    const queryString = new URLSearchParams(defaultParams).toString();
    const url = `${ADMIN_ENDPOINTS.ALL_PROJECTS}?${queryString}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    const result = await handleResponse(response);

    // Transform dữ liệu projects
    if (result.success && result.data) {
      result.data = Array.isArray(result.data)
        ? result.data.map(transformProjectData)
        : [transformProjectData(result.data)];
    }

    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Tạo dự án mới
export async function createProject(projectData) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.CREATE_PROJECT, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(projectData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật dự án
export async function updateProject(projectId, projectData) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.UPDATE_PROJECT(projectId), {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(projectData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khôi phục dự án
export async function restoreProject(projectId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.RESTORE_PROJECT(projectId), {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa vĩnh viễn dự án
export async function deleteProject(projectId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.DELETE_PROJECT(projectId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách dự án đã xóa
export async function getDeletedProjects() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.DELETED_PROJECTS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách dự án đã xóa vĩnh viễn
export async function getPermanentlyDeletedProjects() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.PERMANENTLY_DELETED_PROJECTS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa vĩnh viễn dự án
export async function permanentDeleteProject(projectId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.PERMANENT_DELETE_PROJECT(projectId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khôi phục dự án đã xóa vĩnh viễn
export async function restorePermanentProject(projectId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.RESTORE_PERMANENT_PROJECT(projectId), {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}


// ========== QUẢN LÝ CÔNG VIỆC ==========

// Lấy tất cả công việc
export async function getAllTasks() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.ALL_TASKS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khôi phục công việc đã xóa vĩnh viễn
export async function restorePermanentTask(taskId) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.RESTORE_PERMANENT_TASK(taskId), {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== BẢO TRÌ HỆ THỐNG ==========

// Bật/tắt chế độ bảo trì
export async function toggleMaintenanceMode(data = {}) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.TOGGLE_MAINTENANCE, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy trạng thái bảo trì
export async function getMaintenanceStatus() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.MAINTENANCE_STATUS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Dọn dẹp hệ thống
export async function systemCleanup() {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.SYSTEM_CLEANUP, {
      method: 'POST',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== THAO TÁC HÀNG LOẠT ==========

// Thao tác hàng loạt với người dùng
export async function bulkUserOperations(operation, userIds, data = {}) {
  try {
    const response = await fetch(ADMIN_ENDPOINTS.BULK_USER_OPERATIONS, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ operation, userIds, data }),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== LEGACY FUNCTIONS (để tương thích với code cũ) ==========

// Alias functions để tương thích với code cũ từ HR
export const getAllEmployees = getAllUsers;
export const createEmployee = createUser;
export const updateEmployee = updateUser;
export const toggleEmployeeStatus = toggleUserBlock;
export const deleteEmployee = deleteUser;
export const changeEmployeeRole = changeUserRole;
export const restoreEmployee = restoreUser;
export const getDeletedEmployees = getDeletedUsers;
export const getEmployeeActivities = getUserActivities;

// Alias cho profile functions
export const getHRProfile = getAdminProfile;
export const updateHRProfile = updateAdminProfile;
export const changeHRPassword = changeAdminPassword;
export const getHRColleagues = getAdminColleagues;

// Department functions đã được chuyển sang departmentManagement.js
// Sử dụng: import { getAllDepartments, createDepartment, ... } from '../api/departmentManagement';