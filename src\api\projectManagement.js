 import { 
  ADMIN_ENDPOINTS, 
  CEO_ENDPOINTS, 
  LEADER_ENDPOINTS,
  getCurrentUserEndpoints,
  STATISTICS_ENDPOINTS
} from './endpoints.js';

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;

    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// Lấy endpoints dựa trên role hiện tại
const getCurrentEndpoints = () => {
  return getCurrentUserEndpoints();
};

// ========== PROJECT MANAGEMENT FUNCTIONS ==========

// Transform dữ liệu project từ backend về format frontend
export const transformProjectData = (backendProject) => {
  return {
    id: backendProject._id || backendProject.id,
    _id: backendProject._id || backendProject.id,
    name: backendProject.name || 'Chưa có tên',
    description: backendProject.description || '',
    projectCode: backendProject.projectCode || backendProject.code || `PRJ-${(backendProject._id || backendProject.id || '').toString().slice(-6)}`,
    status: backendProject.status || 'waiting',
    priority: backendProject.priority || 'medium',
    startDate: backendProject.startDate,
    endDate: backendProject.endDate,
    departmentId: backendProject.departmentId,
    leaderId: backendProject.leaderId,
    members: backendProject.members || [],
    followers: backendProject.followers || [],
    createdBy: backendProject.createdBy || backendProject.createdById ? {
      id: (backendProject.createdBy || backendProject.createdById)._id || (backendProject.createdBy || backendProject.createdById).id,
      fullName: (backendProject.createdBy || backendProject.createdById).fullName || (backendProject.createdBy || backendProject.createdById).name || 'Người tạo',
      name: (backendProject.createdBy || backendProject.createdById).fullName || (backendProject.createdBy || backendProject.createdById).name || 'Người tạo',
      avatar: (backendProject.createdBy || backendProject.createdById).avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent((backendProject.createdBy || backendProject.createdById).fullName || (backendProject.createdBy || backendProject.createdById).name || 'U')
    } : null,
    createdAt: backendProject.createdAt,
    updatedAt: backendProject.updatedAt
  };
};

// Lấy tất cả dự án
export async function getAllProjects(params = {}) {
  try {
    // Mặc định lấy tất cả dự án
    const defaultParams = { limit: 1000, ...params };
    const apiUrl = `${import.meta.env.VITE_API_URL || "https://project-management-ji01.onrender.com"}/api/projects`;
    const queryString = new URLSearchParams(defaultParams).toString();
    const url = `${apiUrl}?${queryString}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    const result = await handleResponse(response);

    // Transform dữ liệu projects
    if (result.success && result.data) {
      result.data = Array.isArray(result.data)
        ? result.data.map(transformProjectData)
        : [transformProjectData(result.data)];
    }

    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Tạo dự án mới
export async function createProject(projectData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.CREATE_PROJECT;
    
    if (!apiUrl) {
      throw new Error('Không có quyền tạo dự án');
    }

    // Chuẩn bị dữ liệu dự án theo format API
    const formattedData = {
      name: projectData.name,
      description: projectData.description,
      startDate: projectData.startDate,
      endDate: projectData.endDate,
      status: projectData.status || 'waiting',
      priority: projectData.priority || 'normal',
      departmentId: projectData.departmentId,
      leaderId: projectData.leaderId,
      members: projectData.members?.map(member => ({
        userId: member.userId || member.id
      })) || [],
      followers: projectData.followers || []
    };

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(formattedData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật dự án
export async function updateProject(projectId, projectData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.UPDATE_PROJECT?.(projectId);
    
    if (!apiUrl) {
      throw new Error('Không có quyền cập nhật dự án');
    }

    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(projectData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa dự án
export async function deleteProject(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETE_PROJECT?.(projectId);
    
    if (!apiUrl) {
      throw new Error('Không có quyền xóa dự án');
    }

    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khôi phục dự án
export async function restoreProject(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.RESTORE_PROJECT?.(projectId);
    
    if (!apiUrl) {
      throw new Error('Không có quyền khôi phục dự án');
    }

    const response = await fetch(apiUrl, {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy dự án theo ID
export async function getProjectById(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.GET_PROJECT?.(projectId) || `${endpoints.ALL_PROJECTS}/${projectId}`;

    if (!apiUrl) {
      throw new Error('Không có quyền truy cập dự án');
    }

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    const result = await handleResponse(response);

    // Transform dữ liệu project
    if (result.success && result.data) {
      result.data = transformProjectData(result.data);
    }

    return result;
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy tất cả công việc của dự án
export async function getProjectTasks(projectId) {
  try {
    const apiUrl = `${import.meta.env.VITE_API_URL || "http://localhost:3000"}/api/tasks?projectId=${projectId}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Thêm thành viên vào dự án
export async function addProjectMember(projectId, memberData) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.ADD_PROJECT_MEMBER?.(projectId);
    
    if (!apiUrl) {
      throw new Error('Không có quyền thêm thành viên');
    }

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(memberData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa thành viên khỏi dự án
export async function removeProjectMember(projectId, userId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.REMOVE_PROJECT_MEMBER?.(projectId, userId);
    
    if (!apiUrl) {
      throw new Error('Không có quyền xóa thành viên');
    }

    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách dự án đã xóa
export async function getDeletedProjects() {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.DELETED_PROJECTS;
    
    if (!apiUrl) {
      throw new Error('Không có quyền truy cập dự án đã xóa');
    }

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa vĩnh viễn dự án
export async function permanentDeleteProject(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const apiUrl = endpoints.PERMANENT_DELETE_PROJECT?.(projectId);
    
    if (!apiUrl) {
      throw new Error('Không có quyền xóa vĩnh viễn dự án');
    }

    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
} 

// Lấy thống kê tổng hợp dự án
export async function getProjectOverviewStats() {
  const apiUrl = STATISTICS_ENDPOINTS.ALL_DEPARTMENTS_PROJECTS;
  const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...(localStorage.getItem('token') && { 'Authorization': `Bearer ${localStorage.getItem('token')}` })
    }
  });
  if (!response.ok) {
    const errorText = await response.text();
    console.error('Overview stats error:', errorText);
    throw new Error('Không thể lấy thống kê tổng hợp dự án');
  }
  return response.json();
} 